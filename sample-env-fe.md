# Sample ENV for Frontend

```
VITE_APP_ENV="staging" # production | staging | local

# Application Urls
VITE_APP_API_URL="http://localhost:8001"
VITE_MARKETING_SITE_URL="https://www.example.com/"

# Google recaptcha
VITE_RECAPTCHA_SITE_KEY="{your_key_here}"

# Google maps API key for address lookup
VITE_GOOGLE_MAPS_API_KEY="{your_key_here}"

# For generating payment token to send to server
VITE_PAYPAL_CLIENT_ID="{your_key_here}"

# For customer support
VITE_SUPPORT_EMAIL="<EMAIL>"
# VITE_SUPPORT_PHONE="+****************"

# Site URLs
VITE_TERMS_CONDITIONS_URL="https://www.example.com/terms-and-conditions"
VITE_PRIVACY_POLICY_URL="https://www.example.com/privacy-policy"

# Affiliate
VITE_AFFILIATE_AGREEMENT_URL="https://www.example.com/terms-and-conditions"

# MS Clarity ~ Add variable(s) to enable; Default: Disabled
VITE_MS_CLARITY_PROJECT_ID="{your_key_here}"

# Meta Pixel ID ~ Add variable(s) to enable; Default: Disabled
VITE_META_PIXEL_ID="{your_key_here}"

# For analytics and tracking ~ Add variable(s) to enable; Default: Disabled
VITE_POSTHOG_API_KEY='{your_key_here}'
VITE_POSTHOG_API_HOST='{your_key_here}'
```