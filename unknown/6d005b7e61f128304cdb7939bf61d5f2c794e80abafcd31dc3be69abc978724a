# WLRX Client Onboarding Dashboard

A comprehensive admin dashboard built with **Next.js 15** for tracking White Label RX client onboarding processes. This application provides an intuitive interface for managing multiple clients simultaneously through their complete onboarding journey.

## 🚀 Features

- **Dashboard Overview**: Real-time statistics and progress tracking
- **Client Management**: Add, edit, view, and delete clients
- **Onboarding Tracking**: Complete 7-phase onboarding checklist based on the main.md requirements
- **Progress Visualization**: Interactive progress bars and status indicators
- **Responsive Design**: Mobile-friendly interface with modern UI/UX
- **MongoDB Integration**: Persistent data storage with MongoDB
- **Real-time Updates**: Live progress tracking and updates

## 📋 Onboarding Phases

The application tracks clients through 7 comprehensive phases:

1. **Admin Setup** - Create subcompany and configure basic settings
2. **Codebase Setup** - Fork and customize the codebase
3. **Branding** - Update visual branding elements
4. **Configuration** - Configure backend and frontend environment variables
5. **Deployment** - Set up hosting and deploy application
6. **Testing** - Comprehensive testing of all features and flows
7. **Client Handoff** - Final delivery and training

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS with custom components
- **Database**: MongoDB with Mongoose ODM
- **Icons**: Lucide React
- **Development**: ESLint, Turbopack

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd wlrx-client-onboarding
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Update `.env.local` with your MongoDB connection string:
   ```env
   MONGODB_URI=************************************************************************
   NEXTAUTH_SECRET=your-secret-key-here
   NEXTAUTH_URL=http://localhost:3000
   ```

4. **Start MongoDB (using Docker)**
   ```bash
   docker-compose up -d
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🐳 MongoDB Setup with Docker

The application requires MongoDB. Use the provided Docker Compose configuration:

```yaml
services:
  mongo:
    image: mongo
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: password
    volumes:
      - mongo_data:/data/db

  mongo-express:
    image: mongo-express
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: root
      ME_CONFIG_MONGODB_ADMINPASSWORD: password
      ME_CONFIG_MONGODB_URL: ***********************************/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongo

volumes:
  mongo_data:
```

## 🎯 Usage

### Adding a New Client

1. Click "Add New Client" from the dashboard or clients page
2. Fill in the client information:
   - Client Name (required)
   - Company Name (required)
   - App Base URL
   - Beluga Sub Company Name
   - Assigned Team Member
   - Treatments Interested (ED, HL, WL)
   - Pharmacies (GoGoMeds)
   - Additional Notes

### Tracking Progress

1. Select a client from the dashboard or clients list
2. View the detailed onboarding checklist
3. Check off completed steps and sub-steps
4. Monitor progress through visual indicators
5. Add notes and track completion dates

### Dashboard Features

- **Statistics Cards**: Total clients, completed, in progress, on hold
- **Progress Overview**: Overall completion percentage
- **Recent Activity**: Latest client updates
- **Quick Navigation**: Easy access to all features

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js 15 App Router
│   ├── api/               # API routes
│   │   ├── clients/       # Client CRUD operations
│   │   └── dashboard/     # Dashboard statistics
│   ├── globals.css        # Global styles and custom components
│   ├── layout.tsx         # Root layout
│   └── page.tsx          # Home page
├── components/            # React components
│   ├── clients/          # Client management components
│   ├── layout/           # Layout components
│   ├── onboarding/       # Onboarding tracking components
│   ├── Dashboard.tsx     # Main dashboard
│   └── MainApp.tsx       # Main application router
├── data/                 # Static data and templates
│   └── onboardingTemplate.ts
├── lib/                  # Utility functions
│   ├── mongodb.ts        # Database connection
│   └── utils.ts          # Helper functions
├── models/               # MongoDB schemas
│   └── Client.ts
└── types/                # TypeScript type definitions
    └── onboarding.ts
```

## 🎨 UI/UX Features

- **Modern Design**: Clean, professional interface with Tailwind CSS
- **Responsive Layout**: Works seamlessly on desktop, tablet, and mobile
- **Interactive Elements**: Hover effects, transitions, and animations
- **Progress Visualization**: Gradient progress bars and status indicators
- **Form Validation**: Real-time validation with helpful error messages
- **Loading States**: Smooth loading indicators and skeleton screens

## 🔧 API Endpoints

- `GET /api/clients` - Fetch all clients
- `POST /api/clients` - Create a new client
- `GET /api/clients/[id]` - Fetch a specific client
- `PUT /api/clients/[id]` - Update a client
- `DELETE /api/clients/[id]` - Delete a client
- `GET /api/dashboard/stats` - Get dashboard statistics

## 🚀 Deployment

The application can be deployed on any platform that supports Next.js:

- **Vercel** (recommended)
- **Netlify**
- **AWS**
- **Digital Ocean**
- **Self-hosted**

Make sure to:
1. Set up environment variables on your hosting platform
2. Configure MongoDB connection (MongoDB Atlas recommended for production)
3. Update the `NEXTAUTH_URL` to your production domain

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

Built with ❤️ using Next.js 15 and modern web technologies.
