# White Label Rx — Client Onboarding Checklist

---

## 1️⃣ Admin Setup

- [ ]  **Create subcompany** on admin panel.
    - Company Name
    - App Base URL
    - Beluga Sub Company name
    - Treatments interested in
        - [ ]  ED
        - [ ]  HL
        - [ ]  WL
    - Pharmacies
        - [ ]  GoGoMeds

## 2️⃣ Codebase Setup

- [ ]  Fork White Label Demo repository
- [ ]  Rename repository to client-specific name
- [ ]  Comment/remove modules not requested by client e.g Affiliate module, OTC, or any treatment

## 3️⃣ Branding

- [ ]  Update Logo
- [ ]  Update Brand Colors(if applicable)

## 4️⃣ Configuration

- [ ]  Backend: Set Environment Variables
    - [ ]  Configure App Setting
        - [ ]  `APP_NAME`
        - [ ]  `FRONTEND_URL`
        - [ ]  `SITE_LOGO_DARK`
    - [ ]  Configure Slack Notification
        - [ ]  `LOG_SLACK_WEBHOOK_URL`
        - [ ]  `LOG_SLACK_USERNAME`
    - [ ]  Configure Database
        - [ ]  `DB_DATABASE`
        - [ ]  `DB_USERNAME`
        - [ ]  `DB_PASSWORD`
    - [ ]  Configure S3 Bucket
        - [ ]  `AWS_S3_ACCESS_KEY`
        - [ ]  `AWS_S3_SECRET_KEY`
        - [ ]  `AWS_S3_BUCKET`
        - [ ]  `AWS_S3_BUCKET_REGION`
        - [ ]  `AWS_S3_ASSETS_URL`
    - [ ]  Configure Google V3 Captcha
        - [ ]  `GOOGLE_RECAPTCHA_SITE_KEY`
        - [ ]  `GOOGLE_RECAPTCHA_SECRET_KEY`
    - [ ]  Configure Invoice Seller Address
        - [ ]  `INVOICE_SELLER_ADDRESS`
    - [ ]  Setup Company Information (Name,Address, and Support Email Address)
        - [ ]  `COMPANY_NAME`
        - [ ]  `COMPANY_ADDRESS`
        - [ ]  `COMPANY_SUPPORT_EMAIL`
    - [ ]  Configure Mailgun Email Services
        - [ ]  `MAILGUN_API_URL`
        - [ ]  `MAILGUN_DOMAIN_NAME`
        - [ ]  `MAILGUN_API_KEY`
        - [ ]  `MAILGUN_REPLY_TO`
        - [ ]  `MAILGUN_FROM_DOMAIN_NAME`
    - [ ]  Set up Payment Gateway
        - [ ]  Paypal
            - [ ]  `PAYPAL_MODE` (sandbox/live)
            - [ ]  `PAYPAL_SANDBOX_CLIENT_ID`
            - [ ]  `PAYPAL_SANDBOX_CLIENT_SECRET`
            - [ ]  `PAYPAL_LIVE_CLIENT_ID`
            - [ ]  `PAYPAL_LIVE_CLIENT_SECRET`
    - [ ]  Configure Twilio Account
        - [ ]  `TWILIO_VERIFY_SID`
        - [ ]  `TWILIO_PHONE_NUMBER`
    - [ ]  Configure WhiteLabelRX
        - [ ]  `WHITE_LABEL_RX_API_KEY`
        - [ ]  `WHITE_LABEL_RX_COMPANY_ID`
        - [ ]  `WHITE_LABEL_RX_WEBHOOK_AUTHORIZATION_BEARER_TOKEN`
    - [ ]  Configure Passport Private & Public Keys
        - [ ]  `PASSPORT_PRIVATE_KEY`
        - [ ]  `PASSPORT_PUBLIC_KEY`

        <aside>
        💡

        **Note:**

        Run the following command to generate Passport Private and Public keys:

        ```bash
        php artisan passport:keys
        ```

        The files `oauth-private.key` and `oauth-public.key` will be placed under the `storage/` folder.

        Run the following command to generate the Client ID and Secret Keys for passport personal access client in the database:

        ```bash
        php artisan passport:client --personal
        ```

        </aside>


- [ ]  Frontend: Set Environment Variables:
    - [ ]  Configure Google recaptcha and SET env var: `VITE_RECAPTCHA_SITE_KEY`
    - [ ]  Configure Google maps API and SET env var: `VITE_GOOGLE_MAPS_API_KEY`
    - [ ]  Configure PayPal and SET env var: `VITE_PAYPAL_CLIENT_ID`
    - [ ]  For customer support SET at least one or both: `VITE_SUPPORT_EMAIL` and `VITE_SUPPORT_PHONE`
    - [ ]  For Other analytics related variables check sample file below

        [Sample ENV for Frontend](./sample-env-fe.md)


## 5️⃣ Deployment

- [ ]  Set up hosting environment
- [ ]  Configure domain and SSL
- [ ]  Configute Node JS (NPM)
- [ ]  Configure Composer Command

    ```bash
    composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev --no-progress --classmap-authoritative
    ```

- [ ]  Configure PHP Command (Schedule, Queue, Migrate, Optimize:clear)
    - [ ]  `php artisan schedule:work`
    - [ ]  `php artisan queue:restart`
    - [ ]  `php artisan queue:work`
    - [ ]  `php artisan migrate —force`
    - [ ]  `php artisan optimize:clear`
- [ ]  Deploy application

## 6️⃣ Testing

- [ ]  Verify branding elements e.g. correct logo and dimensions, correct brand name etc
- [ ]  Client Build Admin panel:
    - [ ]  Add products for each treatments; setup recommendations and state mappings where applicable
        - [ ]  ED
        - [ ]  HL
        - [ ]  WL
    - [ ]  OTC
        - [ ]  Add Categories
        - [ ]  Add Products
    - [ ]  Add Shipping Methods
    - [ ]  Add Promo Codes
    - [ ]  Enable/Disable States as per client requirments and Provider Network/Pharmacies limitations
    - [ ]  Some Notable Global settings:
        - [ ]  Consultation fee
        - [ ]  First Order Discount
        - [ ]  Document Upload Stage
        - [ ]  FOR OTC: Guest Checkout, Shipping Fee
    - [ ]  Affiliate Program
        - [ ]  Add Commision Tiers
        - [ ]  Affiliate user payout request approval and rejection
- [ ]  Test core user flows
    - [ ]  ED visit flow
    - [ ]  HL visit flow
    - [ ]  WL visit flow
- [ ]  Test core affiliate flows
    - [ ]  ED
    - [ ]  HL
    - [ ]  WL
    - [ ]  Affiliate Payout request
- [ ]  Test API connection between WhiteLabelRx Admin and Client build
- [ ]  Test visit creation on Beluga and webhook functionality
- [ ]  Test visit creation on GogoMeds and webhook functionality
- [ ]  Promote to PRODUCTION after successfull testing

## 7️⃣ Client Handoff

- [ ]  Deliver admin credentials
- [ ]  Conduct training session for clients

## 📝 Notes

*Client-specific requirements:*

- [ ]  New Treatment request: ______________
- [ ]  New Payment method integration: ______________
- [ ]  New module integration: ______________