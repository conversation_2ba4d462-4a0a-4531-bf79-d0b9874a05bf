'use client';

import { useState } from 'react';
import { 
  Home, 
  Users, 
  Plus, 
  BarChart3, 
  Settings, 
  Menu, 
  X,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface SidebarProps {
  currentView: string;
  onViewChange: (view: string) => void;
}

export default function Sidebar({ currentView, onViewChange }: SidebarProps) {
  const [isOpen, setIsOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: 'dashboard', icon: Home },
    { name: 'All Clients', href: 'clients', icon: Users },
    { name: 'Add Client', href: 'add-client', icon: Plus },
    { name: 'Analytics', href: 'analytics', icon: BarChart3 },
    { name: 'Settings', href: 'settings', icon: Settings },
  ];

  const quickStats = [
    { name: 'Completed', count: 0, icon: CheckCircle, color: 'text-green-600' },
    { name: 'In Progress', count: 0, icon: Clock, color: 'text-blue-600' },
    { name: 'On Hold', count: 0, icon: AlertCircle, color: 'text-yellow-600' },
  ];

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden">
        <button
          type="button"
          className="bg-white p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="sr-only">Open sidebar</span>
          {isOpen ? (
            <X className="block h-6 w-6" aria-hidden="true" />
          ) : (
            <Menu className="block h-6 w-6" aria-hidden="true" />
          )}
        </button>
      </div>

      {/* Sidebar */}
      <div className={`${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <span className="text-lg font-semibold text-gray-900">WLRX Admin</span>
            <button
              type="button"
              className="lg:hidden"
              onClick={() => setIsOpen(false)}
            >
              <X className="h-6 w-6 text-gray-400" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => {
                    onViewChange(item.href);
                    setIsOpen(false);
                  }}
                  className={`${
                    currentView === item.href
                      ? 'bg-indigo-100 text-indigo-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  } group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left`}
                >
                  <Icon
                    className={`${
                      currentView === item.href ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'
                    } mr-3 flex-shrink-0 h-5 w-5`}
                    aria-hidden="true"
                  />
                  {item.name}
                </button>
              );
            })}
          </nav>

          {/* Quick Stats */}
          <div className="px-4 py-4 border-t border-gray-200">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
              Quick Stats
            </h3>
            <div className="space-y-2">
              {quickStats.map((stat) => {
                const Icon = stat.icon;
                return (
                  <div key={stat.name} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Icon className={`h-4 w-4 ${stat.color} mr-2`} />
                      <span className="text-sm text-gray-600">{stat.name}</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{stat.count}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
}
