'use client';

import { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, CheckCircle, Circle, Clock, User, Calendar } from 'lucide-react';
import { Client, OnboardingPhase, OnboardingStep, OnboardingSubStep } from '@/types/onboarding';
import { formatDate } from '@/lib/utils';

interface OnboardingChecklistProps {
  client: Client;
  onUpdateStep: (phaseId: string, stepId: string, completed: boolean, notes?: string) => void;
  onUpdateSubStep: (phaseId: string, subStepId: string, completed: boolean, notes?: string) => void;
}

export default function OnboardingChecklist({ client, onUpdateStep, onUpdateSubStep }: OnboardingChecklistProps) {
  const [expandedPhases, setExpandedPhases] = useState<Set<string>>(new Set());
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Auto-expand current phase and incomplete phases
    const toExpand = new Set<string>();
    client.phases.forEach(phase => {
      if (!phase.completed || phase.order === client.currentPhase) {
        toExpand.add(phase.id);
      }
    });
    setExpandedPhases(toExpand);
  }, [client]);

  const togglePhase = (phaseId: string) => {
    setExpandedPhases(prev => {
      const newSet = new Set(prev);
      if (newSet.has(phaseId)) {
        newSet.delete(phaseId);
      } else {
        newSet.add(phaseId);
      }
      return newSet;
    });
  };

  const toggleStep = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  };

  const handleStepToggle = (phaseId: string, stepId: string, completed: boolean) => {
    onUpdateStep(phaseId, stepId, completed);
  };

  const handleSubStepToggle = (phaseId: string, subStepId: string, completed: boolean) => {
    onUpdateSubStep(phaseId, subStepId, completed);
  };

  const getPhaseIcon = (phase: OnboardingPhase) => {
    if (phase.completed) {
      return <CheckCircle className="w-6 h-6 text-green-500" />;
    } else if (phase.order === client.currentPhase) {
      return <Clock className="w-6 h-6 text-blue-500" />;
    } else {
      return <Circle className="w-6 h-6 text-gray-400" />;
    }
  };

  const getStepIcon = (step: OnboardingStep | OnboardingSubStep, isSubStep = false) => {
    const size = isSubStep ? "w-4 h-4" : "w-5 h-5";
    if (step.completed) {
      return <CheckCircle className={`${size} text-green-500`} />;
    } else {
      return <Circle className={`${size} text-gray-400`} />;
    }
  };

  return (
    <div className="space-y-4 fade-in">
      <div className="card p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{client.name}</h2>
            <p className="text-sm text-gray-500">{client.companyName}</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-gradient">{client.progress.percentage}%</div>
            <div className="text-sm text-gray-500">Complete</div>
          </div>
        </div>

        <div className="mb-6">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Overall Progress</span>
            <span>{client.progress.completedSteps} of {client.progress.totalSteps} steps</span>
          </div>
          <div className="progress-bar h-4">
            <div
              className="progress-fill h-full"
              style={{ width: `${client.progress.percentage}%` }}
            ></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm text-gray-500">Current Phase</div>
            <div className="font-medium">{client.currentPhase} of {client.progress.totalPhases}</div>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm text-gray-500">Status</div>
            <div className="font-medium capitalize">{client.status.replace('_', ' ')}</div>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm text-gray-500">Last Updated</div>
            <div className="font-medium">{formatDate(new Date(client.updatedAt))}</div>
          </div>
        </div>
      </div>

      {/* Phases */}
      <div className="space-y-4">
        {client.phases.map((phase, index) => (
          <div
            key={phase.id}
            className="card-hover transform transition-all duration-200"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <div
              className={`p-4 cursor-pointer border-l-4 transition-all duration-200 ${
                phase.completed
                  ? 'border-green-500 bg-gradient-to-r from-green-50 to-green-25'
                  : phase.order === client.currentPhase
                    ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-blue-25'
                    : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
              }`}
              onClick={() => togglePhase(phase.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getPhaseIcon(phase)}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{phase.title}</h3>
                    <p className="text-sm text-gray-500">{phase.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {phase.completedSteps} / {phase.totalSteps}
                    </div>
                    <div className="text-xs text-gray-500">
                      {phase.totalSteps > 0 ? Math.round((phase.completedSteps / phase.totalSteps) * 100) : 0}%
                    </div>
                  </div>
                  {expandedPhases.has(phase.id) ? (
                    <ChevronDown className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  )}
                </div>
              </div>
            </div>

            {expandedPhases.has(phase.id) && (
              <div className="border-t border-gray-200">
                <div className="p-4 space-y-3">
                  {/* Main Steps */}
                  {phase.steps.map((step) => (
                    <div key={step.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => handleStepToggle(phase.id, step.id, !step.completed)}
                            className="flex-shrink-0"
                          >
                            {getStepIcon(step)}
                          </button>
                          <div className="flex-1">
                            <span className={`text-sm ${step.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                              {step.title}
                            </span>
                            {step.completedAt && (
                              <div className="flex items-center text-xs text-gray-500 mt-1">
                                <Calendar className="w-3 h-3 mr-1" />
                                {formatDate(new Date(step.completedAt))}
                                {step.completedBy && (
                                  <>
                                    <User className="w-3 h-3 ml-2 mr-1" />
                                    {step.completedBy}
                                  </>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                        {phase.subSteps.filter(sub => sub.parentId === step.id).length > 0 && (
                          <button
                            onClick={() => toggleStep(step.id)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            {expandedSteps.has(step.id) ? (
                              <ChevronDown className="w-4 h-4" />
                            ) : (
                              <ChevronRight className="w-4 h-4" />
                            )}
                          </button>
                        )}
                      </div>

                      {/* Sub Steps */}
                      {expandedSteps.has(step.id) && (
                        <div className="ml-8 space-y-2">
                          {phase.subSteps
                            .filter(subStep => subStep.parentId === step.id)
                            .map((subStep) => (
                              <div key={subStep.id} className="flex items-center space-x-3">
                                <button
                                  onClick={() => handleSubStepToggle(phase.id, subStep.id, !subStep.completed)}
                                  className="flex-shrink-0"
                                >
                                  {getStepIcon(subStep, true)}
                                </button>
                                <div className="flex-1">
                                  <span className={`text-sm ${subStep.completed ? 'line-through text-gray-500' : 'text-gray-700'}`}>
                                    {subStep.title}
                                  </span>
                                  {subStep.completedAt && (
                                    <div className="flex items-center text-xs text-gray-500 mt-1">
                                      <Calendar className="w-3 h-3 mr-1" />
                                      {formatDate(new Date(subStep.completedAt))}
                                      {subStep.completedBy && (
                                        <>
                                          <User className="w-3 h-3 ml-2 mr-1" />
                                          {subStep.completedBy}
                                        </>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
