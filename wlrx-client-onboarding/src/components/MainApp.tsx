'use client';

import { useState } from 'react';
import DashboardLayout from './layout/DashboardLayout';
import Dashboard from './Dashboard';
import ClientList from './clients/ClientList';
import ClientDetail from './clients/ClientDetail';
import AddClientForm from './clients/AddClientForm';

type View = 'dashboard' | 'clients' | 'client-detail' | 'add-client' | 'analytics' | 'settings';

export default function MainApp() {
  const [currentView, setCurrentView] = useState<View>('dashboard');
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);

  const handleViewChange = (view: string) => {
    setCurrentView(view as View);
    if (view !== 'client-detail') {
      setSelectedClientId(null);
    }
  };

  const handleClientSelect = (clientId: string) => {
    setSelectedClientId(clientId);
    setCurrentView('client-detail');
  };

  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard onClientSelect={handleClientSelect} />;
      
      case 'clients':
        return <ClientList onClientSelect={handleClientSelect} />;
      
      case 'client-detail':
        if (!selectedClientId) {
          setCurrentView('clients');
          return <ClientList onClientSelect={handleClientSelect} />;
        }
        return (
          <ClientDetail
            clientId={selectedClientId}
            onBack={() => setCurrentView('clients')}
          />
        );
      
      case 'add-client':
        return (
          <div className="max-w-2xl mx-auto">
            <AddClientForm
              onSubmit={async (clientData) => {
                try {
                  const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(clientData),
                  });
                  
                  if (response.ok) {
                    setCurrentView('clients');
                  }
                } catch (error) {
                  console.error('Error adding client:', error);
                }
              }}
              onCancel={() => setCurrentView('clients')}
            />
          </div>
        );
      
      case 'analytics':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Analytics</h2>
            <p className="text-gray-500">Analytics dashboard coming soon...</p>
          </div>
        );
      
      case 'settings':
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Settings</h2>
            <p className="text-gray-500">Settings panel coming soon...</p>
          </div>
        );
      
      default:
        return <Dashboard onClientSelect={handleClientSelect} />;
    }
  };

  return (
    <DashboardLayout currentView={currentView} onViewChange={handleViewChange}>
      {renderContent()}
    </DashboardLayout>
  );
}
