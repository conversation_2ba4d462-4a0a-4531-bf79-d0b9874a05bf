'use client';

import { useState, useEffect } from 'react';
import { ArrowLeft, Edit, Save, X } from 'lucide-react';
import { Client } from '@/types/onboarding';
import { getStatusColor, getStatusLabel } from '@/lib/utils';
import OnboardingChecklist from '../onboarding/OnboardingChecklist';

interface ClientDetailProps {
  clientId: string;
  onBack: () => void;
}

export default function ClientDetail({ clientId, onBack }: ClientDetailProps) {
  const [client, setClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editData, setEditData] = useState<Partial<Client>>({});

  useEffect(() => {
    fetchClient();
  }, [clientId]);

  const fetchClient = async () => {
    try {
      const response = await fetch(`/api/clients/${clientId}`);
      if (response.ok) {
        const data = await response.json();
        setClient(data);
        setEditData(data);
      }
    } catch (error) {
      console.error('Error fetching client:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!client) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editData),
      });

      if (response.ok) {
        const updatedClient = await response.json();
        setClient(updatedClient);
        setEditing(false);
      }
    } catch (error) {
      console.error('Error updating client:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleStepUpdate = async (phaseId: string, stepId: string, completed: boolean, notes?: string) => {
    if (!client) return;

    const updatedClient = { ...client };
    const phase = updatedClient.phases.find(p => p.id === phaseId);
    if (phase) {
      const step = phase.steps.find(s => s.id === stepId);
      if (step) {
        step.completed = completed;
        step.completedAt = completed ? new Date() : undefined;
        step.completedBy = completed ? 'Admin' : undefined; // TODO: Get actual user
        if (notes) step.notes = notes;
      }
    }

    // Update client on server
    try {
      const response = await fetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedClient),
      });

      if (response.ok) {
        const savedClient = await response.json();
        setClient(savedClient);
      }
    } catch (error) {
      console.error('Error updating step:', error);
    }
  };

  const handleSubStepUpdate = async (phaseId: string, subStepId: string, completed: boolean, notes?: string) => {
    if (!client) return;

    const updatedClient = { ...client };
    const phase = updatedClient.phases.find(p => p.id === phaseId);
    if (phase) {
      const subStep = phase.subSteps.find(s => s.id === subStepId);
      if (subStep) {
        subStep.completed = completed;
        subStep.completedAt = completed ? new Date() : undefined;
        subStep.completedBy = completed ? 'Admin' : undefined; // TODO: Get actual user
        if (notes) subStep.notes = notes;
      }
    }

    // Update client on server
    try {
      const response = await fetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedClient),
      });

      if (response.ok) {
        const savedClient = await response.json();
        setClient(savedClient);
      }
    } catch (error) {
      console.error('Error updating substep:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">Client not found</div>
        <button
          onClick={onBack}
          className="text-indigo-600 hover:text-indigo-500"
        >
          Go back to client list
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Clients
          </button>
          <div className="flex items-center space-x-2">
            {editing ? (
              <>
                <button
                  onClick={() => setEditing(false)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <X className="w-4 h-4 mr-1" />
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  {saving ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                  ) : (
                    <Save className="w-4 h-4 mr-1" />
                  )}
                  Save
                </button>
              </>
            ) : (
              <button
                onClick={() => setEditing(true)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {editing ? (
                <input
                  type="text"
                  value={editData.name || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 text-2xl font-bold"
                />
              ) : (
                client.name
              )}
            </h1>
            <p className="text-gray-600">
              {editing ? (
                <input
                  type="text"
                  value={editData.companyName || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, companyName: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                />
              ) : (
                client.companyName
              )}
            </p>
            <div className="mt-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(client.status)}`}>
                {getStatusLabel(client.status)}
              </span>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">App Base URL</label>
              {editing ? (
                <input
                  type="url"
                  value={editData.appBaseUrl || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, appBaseUrl: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 sm:text-sm"
                  placeholder="https://example.com"
                />
              ) : (
                <p className="mt-1 text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{client.appBaseUrl || 'Not set'}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Assigned To</label>
              {editing ? (
                <input
                  type="text"
                  value={editData.assignedTo || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, assignedTo: e.target.value }))}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 sm:text-sm"
                  placeholder="Enter team member name"
                />
              ) : (
                <p className="mt-1 text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{client.assignedTo || 'Unassigned'}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Onboarding Checklist */}
      <OnboardingChecklist
        client={client}
        onUpdateStep={handleStepUpdate}
        onUpdateSubStep={handleSubStepUpdate}
      />
    </div>
  );
}
