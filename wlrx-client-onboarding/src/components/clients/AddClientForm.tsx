'use client';

import { useState } from 'react';
import { X, Plus } from 'lucide-react';

interface AddClientFormProps {
  onSubmit: (clientData: any) => void;
  onCancel: () => void;
  loading?: boolean;
}

export default function AddClientForm({ onSubmit, onCancel, loading = false }: AddClientFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    companyName: '',
    appBaseUrl: '',
    belugaSubCompanyName: '',
    assignedTo: '',
    notes: '',
    treatmentsInterested: {
      ED: false,
      HL: false,
      WL: false,
    },
    pharmacies: {
      GoGoMeds: false,
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      if (name.startsWith('treatment-')) {
        const treatment = name.replace('treatment-', '');
        setFormData(prev => ({
          ...prev,
          treatmentsInterested: {
            ...prev.treatmentsInterested,
            [treatment]: checkbox.checked,
          },
        }));
      } else if (name.startsWith('pharmacy-')) {
        const pharmacy = name.replace('pharmacy-', '');
        setFormData(prev => ({
          ...prev,
          pharmacies: {
            ...prev.pharmacies,
            [pharmacy]: checkbox.checked,
          },
        }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <div className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white rounded-xl shadow-2xl">
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-gray-900">Add New Client</h3>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="px-6 py-6">

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Client Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                required
                value={formData.name}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 sm:text-sm"
                placeholder="Enter client name"
              />
            </div>

            <div>
              <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-2">
                Company Name *
              </label>
              <input
                type="text"
                id="companyName"
                name="companyName"
                required
                value={formData.companyName}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 sm:text-sm"
                placeholder="Enter company name"
              />
            </div>

            <div>
              <label htmlFor="appBaseUrl" className="block text-sm font-medium text-gray-700 mb-2">
                App Base URL
              </label>
              <input
                type="url"
                id="appBaseUrl"
                name="appBaseUrl"
                value={formData.appBaseUrl}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 sm:text-sm"
                placeholder="https://example.com"
              />
            </div>

            <div>
              <label htmlFor="belugaSubCompanyName" className="block text-sm font-medium text-gray-700 mb-2">
                Beluga Sub Company Name
              </label>
              <input
                type="text"
                id="belugaSubCompanyName"
                name="belugaSubCompanyName"
                value={formData.belugaSubCompanyName}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 sm:text-sm"
                placeholder="Enter sub company name"
              />
            </div>

            <div className="md:col-span-2">
              <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700 mb-2">
                Assigned To
              </label>
              <input
                type="text"
                id="assignedTo"
                name="assignedTo"
                value={formData.assignedTo}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 sm:text-sm"
                placeholder="Enter team member name"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Treatments Interested
            </label>
            <div className="grid grid-cols-3 gap-4">
              {['ED', 'HL', 'WL'].map((treatment) => (
                <label key={treatment} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                  <input
                    type="checkbox"
                    name={`treatment-${treatment}`}
                    checked={formData.treatmentsInterested[treatment as keyof typeof formData.treatmentsInterested]}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded transition-colors duration-200"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-700">{treatment}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Pharmacies
            </label>
            <div className="space-y-2">
              <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                <input
                  type="checkbox"
                  name="pharmacy-GoGoMeds"
                  checked={formData.pharmacies.GoGoMeds}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded transition-colors duration-200"
                />
                <span className="ml-3 text-sm font-medium text-gray-700">GoGoMeds</span>
              </label>
            </div>
          </div>

          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              rows={4}
              value={formData.notes}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 sm:text-sm resize-none"
              placeholder="Add any additional notes or requirements..."
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Plus className="w-4 h-4 mr-2" />
              )}
              Add Client
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
}
