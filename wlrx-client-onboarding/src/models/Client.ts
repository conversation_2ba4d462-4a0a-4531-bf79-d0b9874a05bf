import mongoose, { Schema, Document } from 'mongoose';
import { Client } from '@/types/onboarding';

const OnboardingStepSchema = new Schema({
  id: { type: String, required: true },
  title: { type: String, required: true },
  completed: { type: Boolean, default: false },
  notes: { type: String },
  completedAt: { type: Date },
  completedBy: { type: String },
});

const OnboardingSubStepSchema = new Schema({
  id: { type: String, required: true },
  title: { type: String, required: true },
  completed: { type: Boolean, default: false },
  notes: { type: String },
  completedAt: { type: Date },
  completedBy: { type: String },
  parentId: { type: String, required: true },
});

const OnboardingPhaseSchema = new Schema({
  id: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  order: { type: Number, required: true },
  steps: [OnboardingStepSchema],
  subSteps: [OnboardingSubStepSchema],
  completed: { type: Boolean, default: false },
  completedSteps: { type: Number, default: 0 },
  totalSteps: { type: Number, default: 0 },
});

const ClientSchema = new Schema<Client & Document>({
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  companyName: { type: String, required: true },
  appBaseUrl: { type: String },
  belugaSubCompanyName: { type: String },
  treatmentsInterested: {
    ED: { type: Boolean, default: false },
    HL: { type: Boolean, default: false },
    WL: { type: Boolean, default: false },
  },
  pharmacies: {
    GoGoMeds: { type: Boolean, default: false },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  status: { 
    type: String, 
    enum: ['not_started', 'in_progress', 'completed', 'on_hold'],
    default: 'not_started'
  },
  currentPhase: { type: Number, default: 1 },
  progress: {
    completedPhases: { type: Number, default: 0 },
    totalPhases: { type: Number, default: 7 },
    completedSteps: { type: Number, default: 0 },
    totalSteps: { type: Number, default: 0 },
    percentage: { type: Number, default: 0 },
  },
  phases: [OnboardingPhaseSchema],
  notes: { type: String },
  assignedTo: { type: String },
});

// Update the updatedAt field before saving
ClientSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

export default mongoose.models.Client || mongoose.model<Client & Document>('Client', ClientSchema);
