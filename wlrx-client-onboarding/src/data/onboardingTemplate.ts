import { OnboardingTemplate } from '@/types/onboarding';

export const onboardingTemplate: OnboardingTemplate = {
  phases: [
    {
      id: 'admin-setup',
      title: '1️⃣ Admin Setup',
      description: 'Create subcompany and configure basic settings',
      order: 1,
      steps: [
        {
          id: 'create-subcompany',
          title: 'Create subcompany on admin panel',
          completed: false,
        }
      ],
      subSteps: [
        { id: 'company-name', title: 'Company Name', completed: false, parentId: 'create-subcompany' },
        { id: 'app-base-url', title: 'App Base URL', completed: false, parentId: 'create-subcompany' },
        { id: 'beluga-sub-company', title: 'Beluga Sub Company name', completed: false, parentId: 'create-subcompany' },
        { id: 'treatment-ed', title: 'ED Treatment', completed: false, parentId: 'create-subcompany' },
        { id: 'treatment-hl', title: 'HL Treatment', completed: false, parentId: 'create-subcompany' },
        { id: 'treatment-wl', title: 'WL Treatment', completed: false, parentId: 'create-subcompany' },
        { id: 'pharmacy-gogomeds', title: 'GoGoMeds Pharmacy', completed: false, parentId: 'create-subcompany' },
      ]
    },
    {
      id: 'codebase-setup',
      title: '2️⃣ Codebase Setup',
      description: 'Fork and customize the codebase for the client',
      order: 2,
      steps: [
        { id: 'fork-repo', title: 'Fork White Label Demo repository', completed: false },
        { id: 'rename-repo', title: 'Rename repository to client-specific name', completed: false },
        { id: 'remove-modules', title: 'Comment/remove modules not requested by client', completed: false },
      ],
      subSteps: []
    },
    {
      id: 'branding',
      title: '3️⃣ Branding',
      description: 'Update visual branding elements',
      order: 3,
      steps: [
        { id: 'update-logo', title: 'Update Logo', completed: false },
        { id: 'update-colors', title: 'Update Brand Colors (if applicable)', completed: false },
      ],
      subSteps: []
    },
    {
      id: 'configuration',
      title: '4️⃣ Configuration',
      description: 'Configure backend and frontend environment variables',
      order: 4,
      steps: [
        { id: 'backend-env', title: 'Backend: Set Environment Variables', completed: false },
        { id: 'frontend-env', title: 'Frontend: Set Environment Variables', completed: false },
      ],
      subSteps: [
        // Backend configuration substeps
        { id: 'app-name', title: 'APP_NAME', completed: false, parentId: 'backend-env' },
        { id: 'frontend-url', title: 'FRONTEND_URL', completed: false, parentId: 'backend-env' },
        { id: 'site-logo-dark', title: 'SITE_LOGO_DARK', completed: false, parentId: 'backend-env' },
        { id: 'slack-webhook', title: 'LOG_SLACK_WEBHOOK_URL', completed: false, parentId: 'backend-env' },
        { id: 'slack-username', title: 'LOG_SLACK_USERNAME', completed: false, parentId: 'backend-env' },
        { id: 'db-database', title: 'DB_DATABASE', completed: false, parentId: 'backend-env' },
        { id: 'db-username', title: 'DB_USERNAME', completed: false, parentId: 'backend-env' },
        { id: 'db-password', title: 'DB_PASSWORD', completed: false, parentId: 'backend-env' },
        { id: 'aws-s3-access-key', title: 'AWS_S3_ACCESS_KEY', completed: false, parentId: 'backend-env' },
        { id: 'aws-s3-secret-key', title: 'AWS_S3_SECRET_KEY', completed: false, parentId: 'backend-env' },
        { id: 'aws-s3-bucket', title: 'AWS_S3_BUCKET', completed: false, parentId: 'backend-env' },
        { id: 'aws-s3-region', title: 'AWS_S3_BUCKET_REGION', completed: false, parentId: 'backend-env' },
        { id: 'aws-s3-assets-url', title: 'AWS_S3_ASSETS_URL', completed: false, parentId: 'backend-env' },
        { id: 'recaptcha-site-key', title: 'GOOGLE_RECAPTCHA_SITE_KEY', completed: false, parentId: 'backend-env' },
        { id: 'recaptcha-secret-key', title: 'GOOGLE_RECAPTCHA_SECRET_KEY', completed: false, parentId: 'backend-env' },
        { id: 'invoice-seller-address', title: 'INVOICE_SELLER_ADDRESS', completed: false, parentId: 'backend-env' },
        { id: 'company-name-env', title: 'COMPANY_NAME', completed: false, parentId: 'backend-env' },
        { id: 'company-address', title: 'COMPANY_ADDRESS', completed: false, parentId: 'backend-env' },
        { id: 'company-support-email', title: 'COMPANY_SUPPORT_EMAIL', completed: false, parentId: 'backend-env' },
        { id: 'mailgun-api-url', title: 'MAILGUN_API_URL', completed: false, parentId: 'backend-env' },
        { id: 'mailgun-domain', title: 'MAILGUN_DOMAIN_NAME', completed: false, parentId: 'backend-env' },
        { id: 'mailgun-api-key', title: 'MAILGUN_API_KEY', completed: false, parentId: 'backend-env' },
        { id: 'mailgun-reply-to', title: 'MAILGUN_REPLY_TO', completed: false, parentId: 'backend-env' },
        { id: 'mailgun-from-domain', title: 'MAILGUN_FROM_DOMAIN_NAME', completed: false, parentId: 'backend-env' },
        { id: 'paypal-mode', title: 'PAYPAL_MODE', completed: false, parentId: 'backend-env' },
        { id: 'paypal-sandbox-client-id', title: 'PAYPAL_SANDBOX_CLIENT_ID', completed: false, parentId: 'backend-env' },
        { id: 'paypal-sandbox-secret', title: 'PAYPAL_SANDBOX_CLIENT_SECRET', completed: false, parentId: 'backend-env' },
        { id: 'paypal-live-client-id', title: 'PAYPAL_LIVE_CLIENT_ID', completed: false, parentId: 'backend-env' },
        { id: 'paypal-live-secret', title: 'PAYPAL_LIVE_CLIENT_SECRET', completed: false, parentId: 'backend-env' },
        { id: 'twilio-verify-sid', title: 'TWILIO_VERIFY_SID', completed: false, parentId: 'backend-env' },
        { id: 'twilio-phone', title: 'TWILIO_PHONE_NUMBER', completed: false, parentId: 'backend-env' },
        { id: 'wlrx-api-key', title: 'WHITE_LABEL_RX_API_KEY', completed: false, parentId: 'backend-env' },
        { id: 'wlrx-company-id', title: 'WHITE_LABEL_RX_COMPANY_ID', completed: false, parentId: 'backend-env' },
        { id: 'wlrx-webhook-token', title: 'WHITE_LABEL_RX_WEBHOOK_AUTHORIZATION_BEARER_TOKEN', completed: false, parentId: 'backend-env' },
        { id: 'passport-private-key', title: 'PASSPORT_PRIVATE_KEY', completed: false, parentId: 'backend-env' },
        { id: 'passport-public-key', title: 'PASSPORT_PUBLIC_KEY', completed: false, parentId: 'backend-env' },
        // Frontend configuration substeps
        { id: 'vite-recaptcha-site-key', title: 'VITE_RECAPTCHA_SITE_KEY', completed: false, parentId: 'frontend-env' },
        { id: 'vite-google-maps-api', title: 'VITE_GOOGLE_MAPS_API_KEY', completed: false, parentId: 'frontend-env' },
        { id: 'vite-paypal-client-id', title: 'VITE_PAYPAL_CLIENT_ID', completed: false, parentId: 'frontend-env' },
        { id: 'vite-support-email', title: 'VITE_SUPPORT_EMAIL', completed: false, parentId: 'frontend-env' },
        { id: 'vite-support-phone', title: 'VITE_SUPPORT_PHONE', completed: false, parentId: 'frontend-env' },
      ]
    },
    {
      id: 'deployment',
      title: '5️⃣ Deployment',
      description: 'Set up hosting environment and deploy application',
      order: 5,
      steps: [
        { id: 'setup-hosting', title: 'Set up hosting environment', completed: false },
        { id: 'configure-domain', title: 'Configure domain and SSL', completed: false },
        { id: 'configure-nodejs', title: 'Configure Node JS (NPM)', completed: false },
        { id: 'configure-composer', title: 'Configure Composer Command', completed: false },
        { id: 'configure-php', title: 'Configure PHP Commands', completed: false },
        { id: 'deploy-app', title: 'Deploy application', completed: false },
      ],
      subSteps: [
        { id: 'php-schedule', title: 'php artisan schedule:work', completed: false, parentId: 'configure-php' },
        { id: 'php-queue-restart', title: 'php artisan queue:restart', completed: false, parentId: 'configure-php' },
        { id: 'php-queue-work', title: 'php artisan queue:work', completed: false, parentId: 'configure-php' },
        { id: 'php-migrate', title: 'php artisan migrate --force', completed: false, parentId: 'configure-php' },
        { id: 'php-optimize', title: 'php artisan optimize:clear', completed: false, parentId: 'configure-php' },
      ]
    },
    {
      id: 'testing',
      title: '6️⃣ Testing',
      description: 'Comprehensive testing of all features and flows',
      order: 6,
      steps: [
        { id: 'verify-branding', title: 'Verify branding elements', completed: false },
        { id: 'admin-panel-setup', title: 'Client Build Admin panel', completed: false },
        { id: 'test-user-flows', title: 'Test core user flows', completed: false },
        { id: 'test-affiliate-flows', title: 'Test core affiliate flows', completed: false },
        { id: 'test-api-connection', title: 'Test API connection between WhiteLabelRx Admin and Client build', completed: false },
        { id: 'test-beluga-webhook', title: 'Test visit creation on Beluga and webhook functionality', completed: false },
        { id: 'test-gogomeds-webhook', title: 'Test visit creation on GogoMeds and webhook functionality', completed: false },
        { id: 'promote-production', title: 'Promote to PRODUCTION after successful testing', completed: false },
      ],
      subSteps: [
        // Admin panel setup substeps
        { id: 'add-ed-products', title: 'Add ED products', completed: false, parentId: 'admin-panel-setup' },
        { id: 'add-hl-products', title: 'Add HL products', completed: false, parentId: 'admin-panel-setup' },
        { id: 'add-wl-products', title: 'Add WL products', completed: false, parentId: 'admin-panel-setup' },
        { id: 'add-otc-categories', title: 'Add OTC Categories', completed: false, parentId: 'admin-panel-setup' },
        { id: 'add-otc-products', title: 'Add OTC Products', completed: false, parentId: 'admin-panel-setup' },
        { id: 'add-shipping-methods', title: 'Add Shipping Methods', completed: false, parentId: 'admin-panel-setup' },
        { id: 'add-promo-codes', title: 'Add Promo Codes', completed: false, parentId: 'admin-panel-setup' },
        { id: 'enable-disable-states', title: 'Enable/Disable States', completed: false, parentId: 'admin-panel-setup' },
        { id: 'consultation-fee', title: 'Consultation fee', completed: false, parentId: 'admin-panel-setup' },
        { id: 'first-order-discount', title: 'First Order Discount', completed: false, parentId: 'admin-panel-setup' },
        { id: 'document-upload-stage', title: 'Document Upload Stage', completed: false, parentId: 'admin-panel-setup' },
        { id: 'guest-checkout', title: 'Guest Checkout', completed: false, parentId: 'admin-panel-setup' },
        { id: 'shipping-fee', title: 'Shipping Fee', completed: false, parentId: 'admin-panel-setup' },
        { id: 'commission-tiers', title: 'Add Commission Tiers', completed: false, parentId: 'admin-panel-setup' },
        { id: 'affiliate-payout', title: 'Affiliate user payout request approval and rejection', completed: false, parentId: 'admin-panel-setup' },
        // User flows substeps
        { id: 'test-ed-flow', title: 'ED visit flow', completed: false, parentId: 'test-user-flows' },
        { id: 'test-hl-flow', title: 'HL visit flow', completed: false, parentId: 'test-user-flows' },
        { id: 'test-wl-flow', title: 'WL visit flow', completed: false, parentId: 'test-user-flows' },
        // Affiliate flows substeps
        { id: 'test-affiliate-ed', title: 'ED affiliate flow', completed: false, parentId: 'test-affiliate-flows' },
        { id: 'test-affiliate-hl', title: 'HL affiliate flow', completed: false, parentId: 'test-affiliate-flows' },
        { id: 'test-affiliate-wl', title: 'WL affiliate flow', completed: false, parentId: 'test-affiliate-flows' },
        { id: 'test-affiliate-payout', title: 'Affiliate Payout request', completed: false, parentId: 'test-affiliate-flows' },
      ]
    },
    {
      id: 'client-handoff',
      title: '7️⃣ Client Handoff',
      description: 'Final delivery and training',
      order: 7,
      steps: [
        { id: 'deliver-credentials', title: 'Deliver admin credentials', completed: false },
        { id: 'conduct-training', title: 'Conduct training session for clients', completed: false },
      ],
      subSteps: []
    }
  ]
};
