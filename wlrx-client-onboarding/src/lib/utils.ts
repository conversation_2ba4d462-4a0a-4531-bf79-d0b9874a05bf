import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { v4 as uuidv4 } from 'uuid'
import { Client, OnboardingPhase } from '@/types/onboarding'
import { onboardingTemplate } from '@/data/onboardingTemplate'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Status utility functions
export function getStatusColor(status: string): string {
  switch (status) {
    case 'not_started':
      return 'bg-gray-100 text-gray-800'
    case 'in_progress':
      return 'bg-blue-100 text-blue-800'
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'on_hold':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export function getStatusLabel(status: string): string {
  switch (status) {
    case 'not_started':
      return 'Not Started'
    case 'in_progress':
      return 'In Progress'
    case 'completed':
      return 'Completed'
    case 'on_hold':
      return 'On Hold'
    default:
      return 'Unknown'
  }
}

// Date formatting utility
export function formatDate(date: Date): string {
  const now = new Date()
  const diffInMs = now.getTime() - date.getTime()
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))

  if (diffInDays === 0) {
    return 'Today'
  } else if (diffInDays === 1) {
    return 'Yesterday'
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7)
    return `${weeks} week${weeks > 1 ? 's' : ''} ago`
  } else {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
}

// Client initialization and progress utilities

export function initializeClientFromTemplate(
  name: string,
  companyName: string,
  assignedTo?: string
): Omit<Client, '_id'> {
  const clientId = uuidv4()
  const now = new Date()

  // Initialize phases from template
  const phases: OnboardingPhase[] = onboardingTemplate.phases.map(templatePhase => ({
    ...templatePhase,
    completed: false,
    completedSteps: 0,
    totalSteps: templatePhase.steps.length + templatePhase.subSteps.length,
    steps: templatePhase.steps.map(step => ({ ...step })),
    subSteps: templatePhase.subSteps.map(subStep => ({ ...subStep }))
  }))

  // Calculate initial progress
  const totalSteps = phases.reduce((sum, phase) => sum + phase.totalSteps, 0)

  return {
    id: clientId,
    name,
    companyName,
    treatmentsInterested: {
      ED: false,
      HL: false,
      WL: false
    },
    pharmacies: {
      GoGoMeds: false
    },
    createdAt: now,
    updatedAt: now,
    status: 'not_started',
    currentPhase: 1,
    progress: {
      completedPhases: 0,
      totalPhases: phases.length,
      completedSteps: 0,
      totalSteps,
      percentage: 0
    },
    phases,
    assignedTo
  }
}

export function updateClientProgress(client: Client): Client {
  let totalCompletedSteps = 0
  let completedPhases = 0

  // Update each phase's progress
  const updatedPhases = client.phases.map(phase => {
    const completedSteps = [...phase.steps, ...phase.subSteps].filter(step => step.completed).length
    const totalSteps = phase.steps.length + phase.subSteps.length
    const phaseCompleted = completedSteps === totalSteps && totalSteps > 0

    totalCompletedSteps += completedSteps
    if (phaseCompleted) completedPhases++

    return {
      ...phase,
      completed: phaseCompleted,
      completedSteps,
      totalSteps
    }
  })

  // Calculate overall progress
  const totalSteps = client.progress.totalSteps
  const percentage = totalSteps > 0 ? Math.round((totalCompletedSteps / totalSteps) * 100) : 0

  // Determine current phase (first incomplete phase)
  let currentPhase = 1
  for (let i = 0; i < updatedPhases.length; i++) {
    if (!updatedPhases[i].completed) {
      currentPhase = i + 1
      break
    }
  }
  if (completedPhases === updatedPhases.length) {
    currentPhase = updatedPhases.length
  }

  // Determine status
  let status: Client['status'] = 'not_started'
  if (percentage === 100) {
    status = 'completed'
  } else if (percentage > 0) {
    status = 'in_progress'
  }

  return {
    ...client,
    phases: updatedPhases,
    currentPhase,
    status,
    progress: {
      completedPhases,
      totalPhases: updatedPhases.length,
      completedSteps: totalCompletedSteps,
      totalSteps,
      percentage
    },
    updatedAt: new Date()
  }
}
