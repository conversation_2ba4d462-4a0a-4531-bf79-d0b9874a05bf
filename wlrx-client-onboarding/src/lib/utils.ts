import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { Client, OnboardingPhase, OnboardingStep, OnboardingSubStep } from '@/types/onboarding';
import { onboardingTemplate } from '@/data/onboardingTemplate';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateClientId(): string {
  return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function initializeClientFromTemplate(
  name: string, 
  companyName: string,
  assignedTo?: string
): Omit<Client, '_id'> {
  const phases: OnboardingPhase[] = onboardingTemplate.phases.map(phase => {
    const totalSteps = phase.steps.length + phase.subSteps.length;
    return {
      ...phase,
      completed: false,
      completedSteps: 0,
      totalSteps,
    };
  });

  const totalSteps = phases.reduce((sum, phase) => sum + phase.totalSteps, 0);

  return {
    id: generateClientId(),
    name,
    companyName,
    treatmentsInterested: {
      ED: false,
      HL: false,
      WL: false,
    },
    pharmacies: {
      GoGoMeds: false,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    status: 'not_started',
    currentPhase: 1,
    progress: {
      completedPhases: 0,
      totalPhases: phases.length,
      completedSteps: 0,
      totalSteps,
      percentage: 0,
    },
    phases,
    assignedTo,
  };
}

export function calculateProgress(client: Client): Client['progress'] {
  const totalSteps = client.phases.reduce((sum, phase) => sum + phase.totalSteps, 0);
  const completedSteps = client.phases.reduce((sum, phase) => sum + phase.completedSteps, 0);
  const completedPhases = client.phases.filter(phase => phase.completed).length;
  
  return {
    completedPhases,
    totalPhases: client.phases.length,
    completedSteps,
    totalSteps,
    percentage: totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0,
  };
}

export function updatePhaseProgress(phase: OnboardingPhase): OnboardingPhase {
  const allSteps = [...phase.steps, ...phase.subSteps];
  const completedSteps = allSteps.filter(step => step.completed).length;
  const totalSteps = allSteps.length;
  
  return {
    ...phase,
    completedSteps,
    totalSteps,
    completed: completedSteps === totalSteps && totalSteps > 0,
  };
}

export function updateClientProgress(client: Client): Client {
  // Update each phase progress
  const updatedPhases = client.phases.map(updatePhaseProgress);
  
  // Calculate overall progress
  const progress = calculateProgress({ ...client, phases: updatedPhases });
  
  // Determine current phase (first incomplete phase)
  const currentPhaseIndex = updatedPhases.findIndex(phase => !phase.completed);
  const currentPhase = currentPhaseIndex === -1 ? updatedPhases.length : currentPhaseIndex + 1;
  
  // Determine status
  let status: Client['status'] = 'not_started';
  if (progress.completedSteps > 0) {
    status = progress.percentage === 100 ? 'completed' : 'in_progress';
  }
  
  return {
    ...client,
    phases: updatedPhases,
    progress,
    currentPhase,
    status,
    updatedAt: new Date(),
  };
}

export function getPhaseById(client: Client, phaseId: string): OnboardingPhase | undefined {
  return client.phases.find(phase => phase.id === phaseId);
}

export function getStepById(phase: OnboardingPhase, stepId: string): OnboardingStep | OnboardingSubStep | undefined {
  return [...phase.steps, ...phase.subSteps].find(step => step.id === stepId);
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

export function getStatusColor(status: Client['status']): string {
  switch (status) {
    case 'completed':
      return 'text-green-600 bg-green-100';
    case 'in_progress':
      return 'text-blue-600 bg-blue-100';
    case 'on_hold':
      return 'text-yellow-600 bg-yellow-100';
    case 'not_started':
    default:
      return 'text-gray-600 bg-gray-100';
  }
}

export function getStatusLabel(status: Client['status']): string {
  switch (status) {
    case 'completed':
      return 'Completed';
    case 'in_progress':
      return 'In Progress';
    case 'on_hold':
      return 'On Hold';
    case 'not_started':
    default:
      return 'Not Started';
  }
}
