import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Client from '@/models/Client';
import { DashboardStats } from '@/types/onboarding';

export async function GET() {
  try {
    await connectDB();
    
    const clients = await Client.find({});
    
    const stats: DashboardStats = {
      totalClients: clients.length,
      completedClients: clients.filter(client => client.status === 'completed').length,
      inProgressClients: clients.filter(client => client.status === 'in_progress').length,
      onHoldClients: clients.filter(client => client.status === 'on_hold').length,
      averageProgress: clients.length > 0 
        ? Math.round(clients.reduce((sum, client) => sum + client.progress.percentage, 0) / clients.length)
        : 0,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    );
  }
}
