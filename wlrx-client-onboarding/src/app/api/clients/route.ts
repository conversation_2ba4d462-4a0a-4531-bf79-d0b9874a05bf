import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Client from '@/models/Client';
import { initializeClientFromTemplate, updateClientProgress } from '@/lib/utils';

export async function GET() {
  try {
    await connectDB();
    const clients = await Client.find({}).sort({ updatedAt: -1 });
    return NextResponse.json(clients);
  } catch (error) {
    console.error('Error fetching clients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch clients' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    const body = await request.json();
    
    const { name, companyName, assignedTo, treatmentsInterested, pharmacies } = body;
    
    if (!name || !companyName) {
      return NextResponse.json(
        { error: 'Name and company name are required' },
        { status: 400 }
      );
    }

    // Initialize client from template
    const clientData = initializeClientFromTemplate(name, companyName, assignedTo);
    
    // Update treatments and pharmacies if provided
    if (treatmentsInterested) {
      clientData.treatmentsInterested = { ...clientData.treatmentsInterested, ...treatmentsInterested };
    }
    if (pharmacies) {
      clientData.pharmacies = { ...clientData.pharmacies, ...pharmacies };
    }

    const client = new Client(clientData);
    await client.save();

    return NextResponse.json(client, { status: 201 });
  } catch (error) {
    console.error('Error creating client:', error);
    return NextResponse.json(
      { error: 'Failed to create client' },
      { status: 500 }
    );
  }
}
