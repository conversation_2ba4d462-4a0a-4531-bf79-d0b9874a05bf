export interface OnboardingStep {
  id: string;
  title: string;
  completed: boolean;
  notes?: string;
  completedAt?: Date;
  completedBy?: string;
}

export interface OnboardingSubStep extends OnboardingStep {
  parentId: string;
}

export interface OnboardingPhase {
  id: string;
  title: string;
  description: string;
  order: number;
  steps: OnboardingStep[];
  subSteps: OnboardingSubStep[];
  completed: boolean;
  completedSteps: number;
  totalSteps: number;
}

export interface Client {
  _id?: string;
  id: string;
  name: string;
  companyName: string;
  appBaseUrl?: string;
  belugaSubCompanyName?: string;
  treatmentsInterested: {
    ED: boolean;
    HL: boolean;
    WL: boolean;
  };
  pharmacies: {
    GoGoMeds: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
  status: 'not_started' | 'in_progress' | 'completed' | 'on_hold';
  currentPhase: number;
  progress: {
    completedPhases: number;
    totalPhases: number;
    completedSteps: number;
    totalSteps: number;
    percentage: number;
  };
  phases: OnboardingPhase[];
  notes?: string;
  assignedTo?: string;
}

export interface OnboardingTemplate {
  phases: Omit<OnboardingPhase, 'completed' | 'completedSteps' | 'totalSteps'>[];
}

export type ClientStatus = 'not_started' | 'in_progress' | 'completed' | 'on_hold';

export interface DashboardStats {
  totalClients: number;
  completedClients: number;
  inProgressClients: number;
  onHoldClients: number;
  averageProgress: number;
}
