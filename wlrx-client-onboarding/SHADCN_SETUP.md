# shadcn/ui Setup Guide

## Overview
This project has been successfully configured with shadcn/ui components library. shadcn/ui is a collection of reusable components built using Radix UI and Tailwind CSS.

## What's Installed

### Core Dependencies
- `@radix-ui/react-*` - Accessible, unstyled UI primitives
- `class-variance-authority` - For creating component variants
- `clsx` & `tailwind-merge` - For conditional CSS classes
- `lucide-react` - Icon library

### Available Components
The following shadcn/ui components are already installed and ready to use:

- **Button** - Various button styles and variants
- **Card** - Container component with header, content, and footer
- **Input** - Form input field
- **Label** - Form labels
- **Checkbox** - Checkbox input
- **Progress** - Progress bar component
- **Badge** - Status and category badges
- **Alert** - Alert messages and notifications
- **Dialog** - Modal dialogs and overlays

## Usage Examples

### Basic Button
```tsx
import { Button } from "@/components/ui/button"

export function MyComponent() {
  return (
    <Button variant="default" size="default">
      Click me
    </Button>
  )
}
```

### Card Component
```tsx
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

export function MyCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>Card description goes here</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Card content</p>
      </CardContent>
      <CardFooter>
        <p>Card footer</p>
      </CardFooter>
    </Card>
  )
}
```

### Form Elements
```tsx
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"

export function MyForm() {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="email">Email</Label>
        <Input id="email" type="email" placeholder="Enter email" />
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox id="terms" />
        <Label htmlFor="terms">Accept terms</Label>
      </div>
    </div>
  )
}
```

## Adding More Components

To add additional shadcn/ui components, use the CLI:

```bash
npx shadcn@latest add [component-name]
```

Popular components to consider:
- `table` - Data tables
- `dropdown-menu` - Dropdown menus
- `tabs` - Tab navigation
- `toast` - Toast notifications
- `form` - Form handling with validation
- `select` - Select dropdowns
- `textarea` - Multi-line text input
- `switch` - Toggle switches
- `avatar` - User avatars
- `skeleton` - Loading skeletons

## Demo Page

Visit `/shadcn-demo` to see all installed components in action with interactive examples.

## Customization

### Theme Colors
The project uses the "Slate" color scheme. You can modify the CSS variables in `src/app/globals.css` to customize the theme.

### Component Variants
Components support various variants (e.g., `default`, `secondary`, `destructive`, `outline`, `ghost`, `link` for buttons). Check each component's TypeScript definitions for available props.

### Custom Styling
Use the `cn()` utility function from `@/lib/utils` to conditionally apply classes:

```tsx
import { cn } from "@/lib/utils"

<Button className={cn("custom-class", isActive && "active-class")}>
  Button
</Button>
```

## Documentation

- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Radix UI Documentation](https://www.radix-ui.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
